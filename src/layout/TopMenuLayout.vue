<template>
  <div class="top-menu-layout">
    <!-- 顶部主导航 -->
    <div class="header-container">
      <div class="header-content">
        <!-- Logo区域 -->
        <div class="logo-section">
          <img src="@/assets/logo/logo.png" alt="TransitSync" />
          <span class="logo-text">TransitSync</span>
        </div>

        <!-- 主菜单区域 -->
        <div class="main-menu">
          <el-menu
            v-if="topMenus.length > 0"
            :key="activeMainMenu"
            mode="horizontal"
            class="top-menu"
            :default-active="activeMainMenu"
            :active-text-color="'#1890ff'"
            :router="false"
            @select="handleMainMenuSelect"
          >
            <template v-for="menu in topMenus" :key="menu.path">
              <!-- 没有子菜单的普通菜单项 -->
              <el-menu-item
                v-if="!menu.children || menu.children.length === 0"
                :index="menu.path"
              >
                <svg-icon v-if="menu.meta && menu.meta.icon && menu.meta.icon !== '#'" :icon-class="menu.meta.icon" />
                <span>{{ menu.meta?.title }}</span>
              </el-menu-item>

              <!-- 有子菜单的下拉菜单 -->
              <el-sub-menu
                v-else
                :index="menu.path"
                popper-class="top-menu-submenu"
              >
                <template #title>
                  <svg-icon v-if="menu.meta && menu.meta.icon && menu.meta.icon !== '#'" :icon-class="menu.meta.icon" class="menu-icon" />
                  <span class="menu-title">{{ menu.meta?.title }}</span>
                </template>

                <el-menu-item
                  v-for="subItem in getChildrenForMenu(menu)"
                  :key="subItem.path"
                  :index="subItem.path"
                  :class="['sub-menu-item', { 'is-active': route.path === subItem.path }]"
                >
                  <svg-icon v-if="subItem.meta && subItem.meta.icon && subItem.meta.icon !== '#'" :icon-class="subItem.meta.icon" class="sub-menu-icon" />
                  <span class="sub-menu-title">{{ subItem.meta?.title }}</span>
                  <el-tag v-if="subItem.meta?.badge" size="small" type="danger">{{ subItem.meta.badge }}</el-tag>
                  <!-- 选中标识 -->
                  <el-icon v-if="route.path === subItem.path" class="selected-indicator">
                    <Check />
                  </el-icon>
                </el-menu-item>
              </el-sub-menu>
            </template>
          </el-menu>

          <!-- 菜单加载状态 -->
          <div v-else class="loading-menu">
            <el-skeleton animated>
              <template #template>
                <div class="menu-skeleton">
                  <el-skeleton-item variant="text" style="width: 80px; height: 20px; margin-right: 20px" />
                  <el-skeleton-item variant="text" style="width: 100px; height: 20px; margin-right: 20px" />
                  <el-skeleton-item variant="text" style="width: 90px; height: 20px; margin-right: 20px" />
                  <el-skeleton-item variant="text" style="width: 70px; height: 20px" />
                </div>
              </template>
            </el-skeleton>
          </div>
        </div>

        <!-- 右侧功能区 -->
        <div class="header-actions">
          <!-- 租户选择 -->
          <el-select
            v-if="userId === 1 && tenantEnabled"
            v-model="companyName"
            class="tenant-select"
            clearable
            filterable
            reserve-keyword
            :placeholder="$t('navbar.selectTenant')"
            @change="dynamicTenantEvent"
            @clear="dynamicClearEvent"
          >
            <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId" />
            <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
          </el-select>

          <!-- 全屏 -->
          <div class="header-action-item" :title="$t('navbar.full')">
            <screenfull />
          </div>

          <!-- 语言选择 -->
          <div class="header-action-item" :title="$t('navbar.language')">
            <lang-select />
          </div>

          <!-- 尺寸选择 -->
          <div class="header-action-item" :title="$t('navbar.layoutSize')">
            <size-select />
          </div>

          <!-- 消息通知 -->
          <el-popover
            placement="bottom"
            trigger="click"
            transition="el-zoom-in-top"
            :width="300"
            :persistent="false"
            :title="$t('navbar.message')"
          >
            <template #reference>
              <div class="header-action-item" :title="$t('navbar.message')">
                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                  <svg-icon icon-class="message" />
                </el-badge>
              </div>
            </template>
            <template #default>
              <notice />
            </template>
          </el-popover>

          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserCommand" class="user-dropdown">
            <div class="user-section">
              <el-avatar :src="userStore.avatar" :size="32" />
              <span>{{ userStore.name }}</span>
              <el-icon><caret-bottom /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <router-link v-if="!dynamic" to="/user/profile">
                  <el-dropdown-item>{{ $t('navbar.personalCenter') }}</el-dropdown-item>
                </router-link>
                <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                  <span>{{ $t('navbar.layoutSetting') }}</span>
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <span>{{ $t('navbar.logout') }}</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </div>

    <!-- 面包屑 + 标签页区域 -->
    <div class="breadcrumb-container">
      <div class="breadcrumb-content">
        <el-breadcrumb separator=">">
          <el-breadcrumb-item
            v-for="item in breadcrumbList"
            :key="item.path"
            :to="item.isDirectory ? null : item.path"
          >
            <span :class="{ 'breadcrumb-directory': item.isDirectory, 'breadcrumb-link': !item.isDirectory }">
              <svg-icon v-if="item.icon" :icon-class="item.icon" />
              {{ item.title }}
            </span>
          </el-breadcrumb-item>
        </el-breadcrumb>

        <!-- 右侧工具栏区域 -->
        <div class="breadcrumb-actions">
          <!-- 页面标签 -->
          <div class="page-tags" v-if="settingsStore.tagsView">
            <el-tag
              v-for="tag in visitedViews"
              :key="tag.path"
              :closable="!isAffix(tag)"
              @close="closeTag(tag)"
              @click="switchToPage(tag)"
              :type="isActive(tag) ? '' : 'info'"
              class="page-tag"
            >
              {{ tag.title }}
            </el-tag>
          </div>

          <!-- 页面工具栏插槽 -->
          <div id="page-toolbar-container" class="page-toolbar-container">
            <!-- 这里会被页面中的right-toolbar组件填充 -->
          </div>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content-wrapper">
      <!-- 主要内容 -->
      <div class="content-area full-width">
        <router-view v-slot="{ Component, route }">
          <transition :enter-active-class="proxy?.animate.animateList[Math.round(Math.random() * proxy?.animate.animateList.length)]" mode="out-in">
            <keep-alive :include="tagsViewStore.cachedViews">
              <component :is="Component" v-if="!route.meta.link" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
        <iframe-toggle />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Check } from '@element-plus/icons-vue'
import { useUserStore } from '@/store/modules/user'
import { usePermissionStore } from '@/store/modules/permission'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useSettingsStore } from '@/store/modules/settings'
import { useNoticeStore } from '@/store/modules/notice'
import { getTenantList } from '@/api/login'
import { dynamicClear, dynamicTenant } from '@/api/system/tenant'
import { TenantVO } from '@/api/types'
import { constantRoutes } from '@/router'
import Notice from './components/notice/index.vue'
import IframeToggle from './components/IframeToggle/index.vue'
import { ElMessageBox, ElMessageBoxOptions } from 'element-plus'
import router from '@/router'

const { proxy } = getCurrentInstance() as ComponentInternalInstance
const route = useRoute()
const userStore = useUserStore()
const permissionStore = usePermissionStore()
const tagsViewStore = useTagsViewStore()
const settingsStore = useSettingsStore()
const noticeStore = storeToRefs(useNoticeStore())

// 租户相关
const userId = ref(userStore.userId)
const companyName = ref(undefined)
const tenantList = ref<TenantVO[]>([])
const dynamic = ref(false)
const tenantEnabled = ref(true)
const newNotice = ref(0)

// 所有的路由信息
const routers = computed(() => permissionStore.getTopbarRoutes())

// 从权限存储获取顶部菜单（这些是从接口动态加载的）
const topMenus = computed(() => {
  const topMenus: any[] = []
  routers.value.forEach(menu => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === '/' && menu.children) {
        topMenus.push(menu.children ? menu.children[0] : menu)
      } else {
        topMenus.push(menu)
      }
    }
  })

  return topMenus
})

// 设置子路由
const childrenMenus = computed(() => {
  const childrenMenus: any[] = []
  routers.value.forEach((router) => {
    if (router.children) {
      router.children.forEach((item) => {
        // 创建子路由的副本，避免修改原始数据
        const childRoute = { ...item }

        if (childRoute.parentPath === undefined) {
          if (router.path === '/') {
            childRoute.path = '/' + childRoute.path
          } else {
            if (!isHttp(childRoute.path)) {
              // 确保路径正确拼接
              if (childRoute.path.startsWith('/')) {
                // 如果子路径已经是绝对路径，直接使用
                childRoute.path = childRoute.path
              } else {
                // 如果是相对路径，拼接父路径
                childRoute.path = router.path + '/' + childRoute.path
              }
            }
          }
          childRoute.parentPath = router.path
        }
        childrenMenus.push(childRoute)
      })
    }
  })
  return constantRoutes.concat(childrenMenus)
})

// 当前激活的主菜单
const activeMainMenu = computed(() => {
  const path = route.path
  console.log('计算activeMainMenu, 当前路径:', path)
  console.log('可用的topMenus:', topMenus.value.map(m => m.path))

  // 如果是首页，激活首页
  if (path === '/' || path === '/index') {
    return '/index'
  }

  // 先查找是否有主菜单直接匹配
  const directMatch = topMenus.value.find(menu => menu.path === path)
  if (directMatch) {
    console.log('直接匹配到主菜单:', directMatch.path)
    return path
  }

  // 查找包含当前路径的主菜单
  for (const menu of topMenus.value) {
    if (menu.children && menu.children.length > 0) {
      // 查找子菜单中是否有匹配的路径
      const hasActiveChild = childrenMenus.value.some(child =>
        child.parentPath === menu.path && child.path === path
      )
      if (hasActiveChild) {
        console.log('通过子菜单匹配到主菜单:', menu.path, '当前页面:', path)
        return menu.path
      }
    }
  }

  // 如果没有找到精确匹配，尝试按路径前缀匹配
  for (const menu of topMenus.value) {
    if (path.startsWith(menu.path + '/') || path === menu.path) {
      console.log('通过前缀匹配到主菜单:', menu.path)
      return menu.path
    }
  }

  console.log('未找到匹配的主菜单')
  return ''
})

// 面包屑导航
const breadcrumbList = computed(() => {
  const matched = route.matched.filter(item => item.meta && item.meta.title)
  return matched.map((item, index) => {
    // 检查是否为目录项（最后一项总是可点击的，其他项需要检查是否为纯目录）
    const isDirectory = index < matched.length - 1 && (!item.component || item.redirect === 'noRedirect')

    return {
      path: item.path,
      title: item.meta?.title,
      icon: item.meta?.icon,
      isDirectory: isDirectory
    }
  })
})

// 访问过的页面
const visitedViews = computed(() => tagsViewStore.visitedViews)

// 路由数据是否已加载完成
const isRoutesLoaded = computed(() => {
  return topMenus.value && topMenus.value.length > 0 && childrenMenus.value && childrenMenus.value.length > 0
})

// 主菜单选择处理 - 修复路径匹配问题
const handleMainMenuSelect = async (index: string) => {
  console.log('点击菜单:', index)

  // 如果路由数据还没加载完成，等待一下
  if (!isRoutesLoaded.value) {
    console.warn('菜单数据尚未加载完成，等待加载...')
    let retryCount = 0
    while (!isRoutesLoaded.value && retryCount < 30) {
      await new Promise(resolve => setTimeout(resolve, 100))
      retryCount++
    }

    if (!isRoutesLoaded.value) {
      console.error('路由数据加载超时')
      return
    }
  }

  if (isHttp(index)) {
    // http(s):// 路径新窗口打开
    window.open(index, '_blank')
    return
  }

  // 优先在childrenMenus中查找完整路径匹配
  const childRoute = childrenMenus.value.find((item) => item.path === index)
  if (childRoute) {
    console.log('找到子路由，直接跳转:', index)
    await router.push({ path: index })
    // 使用nextTick确保路由更新后再触发菜单重新计算
    await nextTick()
    return
  }

  // 然后查找主菜单
  const selectedRoute = topMenus.value.find((item) => item.path === index)
  if (selectedRoute) {
    if (selectedRoute.children && selectedRoute.children.length > 0) {
      // 有子路由，跳转到第一个可用的子路由
      const activeRoutes = getActiveRoutes(index)
      if (activeRoutes.length > 0) {
        const firstRoute = activeRoutes[0]
        console.log('跳转到第一个子路由:', firstRoute.path)
        await router.push({ path: firstRoute.path })
        await nextTick()
      } else {
        console.warn('未找到子路由，尝试直接跳转到:', index)
        await router.push({ path: index })
        await nextTick()
      }
    } else {
      // 没有子路由，直接跳转
      console.log('主菜单直接跳转:', index)
      await router.push({ path: index })
      await nextTick()
    }
    return
  }

  // 最后尝试直接跳转
  console.warn('未找到匹配的路由，尝试直接跳转到:', index)
  await router.push({ path: index })
  await nextTick()
}

// 获取指定主菜单的子菜单列表（使用处理后的完整路径）
const getChildrenForMenu = (menu: any) => {
  return childrenMenus.value.filter(child =>
    child.parentPath === menu.path && !child.hidden
  )
}

// 获取激活的子路由
const getActiveRoutes = (key: string) => {
  const routes: any[] = []
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    childrenMenus.value.forEach((item) => {
      if (key === item.parentPath || (key === 'index' && '' === item.path)) {
        routes.push(item)
      }
    })
  }
  return routes
}

// 判断是否为HTTP链接
const isHttp = (url: string) => {
  return url.indexOf('http://') !== -1 || url.indexOf('https://') !== -1;
}

// 用户命令处理
const handleUserCommand = (command: string) => {
  switch (command) {
    case 'logout':
      logout()
      break
    case 'setLayout':
      // 打开设置面板
      break
  }
}

// 退出登录
const logout = async () => {
  await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  } as ElMessageBoxOptions)

  userStore.logout().then(() => {
    router.replace({
      path: '/login',
      query: {
        redirect: encodeURIComponent(route.fullPath || '/')
      }
    })
    proxy?.$tab.closeAllPage()
  })
}

// 租户相关方法
const dynamicTenantEvent = async (tenantId: string) => {
  if (companyName.value != null && companyName.value !== '') {
    await dynamicTenant(tenantId)
    dynamic.value = true
    await router.push('/')
    await proxy?.$tab.closeAllPage()
    await proxy?.$tab.refreshPage()
  }
}

const dynamicClearEvent = async () => {
  await dynamicClear()
  dynamic.value = false
  await router.push('/')
  await proxy?.$tab.closeAllPage()
  await proxy?.$tab.refreshPage()
}

const initTenantList = async () => {
  const { data } = await getTenantList(true)
  tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled
  if (tenantEnabled.value) {
    tenantList.value = data.voList
  }
}

// 标签相关方法
const isActive = (tag: any) => {
  return tag.path === route.path
}

const isAffix = (tag: any) => {
  return tag?.meta && tag?.meta?.affix
}

const closeTag = (tag: any) => {
  proxy?.$tab.closePage(tag)
}

const switchToPage = (tag: any) => {
  if (tag.path !== route.path) {
    router.push(tag.path)
  }
}

// 监听消息通知
watch(
  () => noticeStore.state.value.notices,
  (newVal) => {
    newVal && (newNotice.value = newVal.filter((item: any) => !item.read).length)
  },
  { deep: true }
)

onMounted(() => {
  initTenantList()
})

defineExpose({
  initTenantList
})
</script>

<style lang="scss" scoped>
.top-menu-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header-container {
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgba(0,21,41,.08);
  z-index: 1000;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #1890ff 0%, #722ed1 50%, #1890ff 100%);
    opacity: 0.1;
  }

  .header-content {
    display: flex;
    align-items: center;
    height: 64px;
    padding: 0 24px;

    .logo-section {
      display: flex;
      align-items: center;
      margin-right: 48px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
      }

      img {
        height: 36px;
        margin-right: 12px;
        filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.2));
      }

      .logo-text {
        font-size: 20px;
        font-weight: 600;
        background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        letter-spacing: 0.5px;
      }
    }

    .main-menu {
      flex: 1;

      .loading-menu {
        display: flex;
        align-items: center;
        height: 64px;

        .menu-skeleton {
          display: flex;
          align-items: center;
        }
      }

      :deep(.top-menu) {
        border-bottom: none;
        background: transparent;

        .el-menu-item,
        .el-sub-menu > .el-sub-menu__title {
          height: 64px;
          line-height: 64px;
          margin: 0 8px;
          border-radius: 8px;
          position: relative;
          font-weight: 500;
          transition: all 0.3s ease;

          &::before {
            content: '';
            position: absolute;
            bottom: 8px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, #1890ff 0%, #722ed1 100%);
            transition: all 0.3s ease;
            border-radius: 1px;
          }

          &:hover {
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(114, 46, 209, 0.05) 100%);
            color: #1890ff;
            transform: translateY(-1px);

            &::before {
              width: 80%;
            }
          }

          &.is-active {
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.12) 0%, rgba(114, 46, 209, 0.08) 100%);
            color: #1890ff;

            &::before {
              width: 100%;
            }
          }

          .svg-icon {
            margin-right: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
          }

          &:hover .svg-icon {
            transform: scale(1.1);
          }
        }

        .el-sub-menu {
          .el-sub-menu__title {
            .svg-icon {
              margin-right: 12px;
              font-size: 16px;
              transition: all 0.3s ease;
            }

            &:hover {
              background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(114, 46, 209, 0.05) 100%);
              color: #1890ff;
            }

            .el-sub-menu__icon-arrow {
              transition: all 0.3s ease;
              color: #1890ff;
            }
          }

          &.is-opened > .el-sub-menu__title .el-sub-menu__icon-arrow {
            transform: rotateZ(180deg);
          }
        }
      }
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .tenant-select {
        width: 200px;

        :deep(.el-input__wrapper) {
          border-radius: 8px;
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
        }
      }

      .header-action-item {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        cursor: pointer;
        border-radius: 8px;
        transition: all 0.3s ease;
        color: #666;
        position: relative;

        &:hover {
          background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(114, 46, 209, 0.05) 100%);
          color: #1890ff;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
        }

        .svg-icon {
          font-size: 18px;
        }

        // 确保内部组件样式正确
        > * {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .user-dropdown {
        .user-section {
          display: flex;
          align-items: center;
          gap: 10px;
          cursor: pointer;
          padding: 8px 16px;
          border-radius: 8px;
          transition: all 0.3s ease;
          font-weight: 500;

          &:hover {
            background: linear-gradient(135deg, rgba(24, 144, 255, 0.08) 0%, rgba(114, 46, 209, 0.05) 100%);
            color: #1890ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(24, 144, 255, 0.15);
          }

          .el-avatar {
            border: 2px solid rgba(24, 144, 255, 0.2);
            transition: all 0.3s ease;
          }

          &:hover .el-avatar {
            border-color: #1890ff;
            transform: scale(1.05);
          }
        }
      }
    }
  }
}

// 全局下拉菜单样式
:deep(.top-menu-submenu) {
  margin-top: 8px !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  border: 1px solid #e4e7ed !important;
  background: #fff !important;

  .el-menu {
    background: transparent !important;
    border: none !important;
    padding: 8px 0 !important;
  }

  .el-menu-item {
    height: 48px !important;
    line-height: 48px !important;
    padding: 0 24px !important;
    margin: 2px 8px !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    color: #606266 !important;
    display: flex !important;
    align-items: center !important;

    &:hover {
      background-color: #f0f9ff !important;
      color: #1890ff !important;
      transform: translateX(2px) !important;
    }

    &.is-active {
      background-color: #e6f7ff !important;
      color: #1890ff !important;
      font-weight: 500 !important;
    }

    .svg-icon,
    .sub-menu-icon {
      margin-right: 20px !important;
      font-size: 16px !important;
      opacity: 0.8 !important;
      transition: all 0.3s ease !important;
      flex-shrink: 0 !important;
      width: 16px !important;
      height: 16px !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
      min-width: 16px !important;
    }

    .sub-menu-title {
      margin-left: 10px;
      font-size: 14px !important;
      line-height: 1.4 !important;
    }

    &:hover .svg-icon {
      opacity: 1 !important;
      transform: scale(1.1) !important;
    }

    span {
      flex: 1 !important;
      text-align: left !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }

    .el-tag {
      margin-left: 12px !important;
      font-size: 10px !important;
      height: 18px !important;
      line-height: 16px !important;
      padding: 0 6px !important;
      border-radius: 9px !important;
      flex-shrink: 0 !important;
    }

    .selected-indicator {
      margin-left: auto !important;
      color: #1890ff !important;
      font-size: 14px !important;
      flex-shrink: 0 !important;
    }
  }

  // 分割线样式
  .el-menu-item + .el-menu-item {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 20px;
      right: 20px;
      height: 1px;
      background: linear-gradient(90deg, transparent 0%, #e4e7ed 50%, transparent 100%);
      opacity: 0.5;
    }
  }
}

.breadcrumb-container {
  background: linear-gradient(135deg, #f8f9fa 0%, #fff 100%);
  border-bottom: 1px solid #e4e7ed;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(24, 144, 255, 0.1) 50%, transparent 100%);
  }

  .breadcrumb-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 48px;
    padding: 0 24px;

    .breadcrumb-actions {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    :deep(.el-breadcrumb) {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          color: #666;
          font-weight: 500;
          transition: all 0.3s ease;

          &:hover {
            color: #1890ff;
            transform: translateX(2px);
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: #1890ff;
          font-weight: 600;
          background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .el-breadcrumb__separator {
          color: #ccc;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        &:hover + .el-breadcrumb__item .el-breadcrumb__separator {
          color: #1890ff;
        }
      }
    }

    .breadcrumb-directory {
      color: #999 !important;
      cursor: default !important;

      .svg-icon {
        opacity: 0.6;
      }
    }

    .breadcrumb-link {
      color: #666;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        color: #1890ff;
        transform: translateX(2px);
      }

      .svg-icon {
        margin-right: 6px;
      }
    }

    .page-tags {
      display: flex;
      gap: 8px;
      align-items: center;

      .page-tag {
        cursor: pointer;
        transition: all 0.3s ease;
        border-radius: 16px;
        font-size: 12px;
        height: 28px;
        line-height: 26px;
        padding: 0 12px;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }

        &.el-tag--primary {
          background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
          border: none;
          color: white;
        }

        &.el-tag--info {
          background: rgba(24, 144, 255, 0.1);
          border: 1px solid rgba(24, 144, 255, 0.2);
          color: #1890ff;
        }
      }
    }

    .page-toolbar-container {
      display: flex;
      align-items: center;

      // 确保工具栏组件样式正确
      :deep(.right-toolbar) {
        display: flex;
        align-items: center;
        gap: 8px;

        .el-button {
          height: 32px;
          padding: 0 12px;
          font-size: 12px;
          border-radius: 6px;

          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

.main-content-wrapper {
  flex: 1;
  display: flex;
  overflow: hidden;

  .content-area {
    flex: 1;
    padding: 20px;
    overflow: auto;
    position: relative;

    &.full-width {
      padding: 20px 40px;
    }
  }
}

// 动画样式
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
