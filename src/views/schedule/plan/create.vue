<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>排班计划制定</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>
      
      <div class="create-content">
        <el-alert
          title="排班计划制定页面"
          description="此页面用于制定新的排班计划，包括选择模板、分配资源等。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="排班计划制定功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="PlanCreate">
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-content {
  padding: 20px;
}
</style>
