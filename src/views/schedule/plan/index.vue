<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>计划管理</span>
          <div>
            <el-button type="primary" @click="handleCreate">制定计划</el-button>
            <el-button type="warning" @click="handleAdjust">临时调整</el-button>
          </div>
        </div>
      </template>
      
      <div class="plan-content">
        <el-alert
          title="排班计划管理"
          description="此页面用于管理排班计划的制定和临时调整。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="计划管理功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="SchedulePlan">
const { proxy } = getCurrentInstance();

function handleCreate() {
  proxy.$router.push('/schedule/plan/create');
}

function handleAdjust() {
  proxy.$router.push('/schedule/plan/adjust');
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plan-content {
  padding: 20px;
}
</style>
