<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧：线路列表 -->
      <el-col :span="8">
        <el-card class="box-card">
          <template #header>
            <div class="card-header">
              <span>线路列表</span>
              <el-button type="text" @click="refreshRoutes">刷新</el-button>
            </div>
          </template>
          
          <div class="route-list">
            <div
              v-for="route in routeList"
              :key="route.routeId"
              class="route-item"
              :class="{ active: selectedRoute?.routeId === route.routeId }"
              @click="selectRoute(route)"
            >
              <div class="route-info">
                <div class="route-name">{{ route.routeName }}</div>
                <div class="route-detail">
                  {{ route.startStation }} - {{ route.endStation }}
                </div>
              </div>
              <div class="route-stats">
                <el-tag size="small" type="info">车辆: {{ route.vehicleCount }}</el-tag>
                <el-tag size="small" type="success">司机: {{ route.driverCount }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧：资源分配详情 -->
      <el-col :span="16">
        <el-card class="box-card" v-if="selectedRoute">
          <template #header>
            <div class="card-header">
              <span>{{ selectedRoute.routeName }} - 资源分配</span>
              <div>
                <el-button type="primary" @click="handleVehicleAllocation">车辆分配</el-button>
                <el-button type="success" @click="handleDriverAllocation">司机分配</el-button>
              </div>
            </div>
          </template>
          
          <el-tabs v-model="activeTab" type="card">
            <!-- 车辆分配 -->
            <el-tab-pane label="车辆分配" name="vehicle">
              <div class="allocation-section">
                <div class="section-header">
                  <h4>已分配车辆 ({{ allocatedVehicles.length }})</h4>
                  <el-button type="primary" size="small" @click="addVehicle">添加车辆</el-button>
                </div>
                
                <el-table :data="allocatedVehicles" style="width: 100%">
                  <el-table-column prop="plateNumber" label="车牌号" />
                  <el-table-column prop="vehicleNumber" label="车辆编号" />
                  <el-table-column prop="brandModel" label="品牌型号" />
                  <el-table-column prop="status" label="状态">
                    <template #default="scope">
                      <el-tag v-if="scope.row.status === '1'" type="success">正常</el-tag>
                      <el-tag v-else-if="scope.row.status === '2'" type="warning">维修</el-tag>
                      <el-tag v-else type="danger">停用</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button link type="danger" @click="removeVehicle(scope.row)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            
            <!-- 司机分配 -->
            <el-tab-pane label="司机分配" name="driver">
              <div class="allocation-section">
                <div class="section-header">
                  <h4>已分配司机 ({{ allocatedDrivers.length }})</h4>
                  <el-button type="success" size="small" @click="addDriver">添加司机</el-button>
                </div>
                
                <el-table :data="allocatedDrivers" style="width: 100%">
                  <el-table-column prop="employeeId" label="工号" />
                  <el-table-column prop="driverName" label="姓名" />
                  <el-table-column prop="phone" label="联系电话" />
                  <el-table-column prop="licenseType" label="准驾车型" />
                  <el-table-column prop="status" label="状态">
                    <template #default="scope">
                      <el-tag v-if="scope.row.status === '1'" type="success">在职</el-tag>
                      <el-tag v-else type="danger">离职</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button link type="danger" @click="removeDriver(scope.row)">移除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>
            
            <!-- 分配统计 -->
            <el-tab-pane label="分配统计" name="statistics">
              <div class="statistics-section">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card class="stat-card">
                      <div class="stat-item">
                        <div class="stat-value">{{ allocatedVehicles.length }}</div>
                        <div class="stat-label">已分配车辆</div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="stat-card">
                      <div class="stat-item">
                        <div class="stat-value">{{ allocatedDrivers.length }}</div>
                        <div class="stat-label">已分配司机</div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
                
                <el-row :gutter="20" style="margin-top: 20px;">
                  <el-col :span="12">
                    <el-card class="stat-card">
                      <div class="stat-item">
                        <div class="stat-value">{{ normalVehicleCount }}</div>
                        <div class="stat-label">正常车辆</div>
                      </div>
                    </el-card>
                  </el-col>
                  <el-col :span="12">
                    <el-card class="stat-card">
                      <div class="stat-item">
                        <div class="stat-value">{{ activeDriverCount }}</div>
                        <div class="stat-label">在职司机</div>
                      </div>
                    </el-card>
                  </el-col>
                </el-row>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-card>
        
        <!-- 未选择线路时的提示 -->
        <el-card class="box-card" v-else>
          <el-empty description="请选择左侧线路查看资源分配详情" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加车辆对话框 -->
    <el-dialog title="添加车辆" v-model="vehicleDialogOpen" width="600px" append-to-body>
      <el-table
        :data="availableVehicles"
        @selection-change="handleVehicleSelection"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="plateNumber" label="车牌号" />
        <el-table-column prop="vehicleNumber" label="车辆编号" />
        <el-table-column prop="brandModel" label="品牌型号" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag v-if="scope.row.status === '1'" type="success">正常</el-tag>
            <el-tag v-else-if="scope.row.status === '2'" type="warning">维修</el-tag>
            <el-tag v-else type="danger">停用</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmAddVehicle">确 定</el-button>
          <el-button @click="vehicleDialogOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加司机对话框 -->
    <el-dialog title="添加司机" v-model="driverDialogOpen" width="600px" append-to-body>
      <el-table
        :data="availableDrivers"
        @selection-change="handleDriverSelection"
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="employeeId" label="工号" />
        <el-table-column prop="driverName" label="姓名" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="licenseType" label="准驾车型" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag v-if="scope.row.status === '1'" type="success">在职</el-tag>
            <el-tag v-else type="danger">离职</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmAddDriver">确 定</el-button>
          <el-button @click="driverDialogOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ResourceAllocation">
const { proxy } = getCurrentInstance();

const routeList = ref([]);
const selectedRoute = ref(null);
const activeTab = ref("vehicle");
const allocatedVehicles = ref([]);
const allocatedDrivers = ref([]);
const availableVehicles = ref([]);
const availableDrivers = ref([]);
const vehicleDialogOpen = ref(false);
const driverDialogOpen = ref(false);
const selectedVehicles = ref([]);
const selectedDrivers = ref([]);

// 计算属性
const normalVehicleCount = computed(() => {
  return allocatedVehicles.value.filter(v => v.status === '1').length;
});

const activeDriverCount = computed(() => {
  return allocatedDrivers.value.filter(d => d.status === '1').length;
});

/** 获取线路列表 */
function getRouteList() {
  // 模拟数据
  routeList.value = [
    {
      routeId: 1,
      routeName: "1路",
      startStation: "火车站",
      endStation: "体育中心",
      vehicleCount: 8,
      driverCount: 16
    },
    {
      routeId: 2,
      routeName: "2路",
      startStation: "汽车站",
      endStation: "机场",
      vehicleCount: 6,
      driverCount: 12
    },
    {
      routeId: 3,
      routeName: "3路",
      startStation: "市中心",
      endStation: "工业园区",
      vehicleCount: 0,
      driverCount: 0
    }
  ];
}

/** 选择线路 */
function selectRoute(route) {
  selectedRoute.value = route;
  loadRouteAllocation(route.routeId);
}

/** 加载线路资源分配 */
function loadRouteAllocation(routeId) {
  // 模拟加载已分配的车辆和司机
  if (routeId === 1) {
    allocatedVehicles.value = [
      {
        vehicleId: 1,
        plateNumber: "京A12345",
        vehicleNumber: "V001",
        brandModel: "宇通ZK6105HNG",
        status: "1"
      },
      {
        vehicleId: 2,
        plateNumber: "京A12346",
        vehicleNumber: "V002",
        brandModel: "比亚迪K9",
        status: "1"
      }
    ];
    
    allocatedDrivers.value = [
      {
        driverId: 1,
        employeeId: "D001",
        driverName: "张三",
        phone: "13800138001",
        licenseType: "A1",
        status: "1"
      },
      {
        driverId: 2,
        employeeId: "D002",
        driverName: "李四",
        phone: "13800138002",
        licenseType: "A3",
        status: "1"
      }
    ];
  } else {
    allocatedVehicles.value = [];
    allocatedDrivers.value = [];
  }
}

/** 获取可用车辆 */
function getAvailableVehicles() {
  availableVehicles.value = [
    {
      vehicleId: 3,
      plateNumber: "京A12347",
      vehicleNumber: "V003",
      brandModel: "金龙XMQ6127",
      status: "1"
    },
    {
      vehicleId: 4,
      plateNumber: "京A12348",
      vehicleNumber: "V004",
      brandModel: "宇通ZK6125HNG",
      status: "2"
    }
  ];
}

/** 获取可用司机 */
function getAvailableDrivers() {
  availableDrivers.value = [
    {
      driverId: 3,
      employeeId: "D003",
      driverName: "王五",
      phone: "13800138003",
      licenseType: "A1",
      status: "1"
    },
    {
      driverId: 4,
      employeeId: "D004",
      driverName: "赵六",
      phone: "13800138004",
      licenseType: "A3",
      status: "1"
    }
  ];
}

function refreshRoutes() {
  getRouteList();
  proxy.$modal.msgSuccess("线路列表已刷新");
}

function handleVehicleAllocation() {
  proxy.$router.push('/schedule/resource/vehicle');
}

function handleDriverAllocation() {
  proxy.$router.push('/schedule/resource/driver');
}

function addVehicle() {
  getAvailableVehicles();
  vehicleDialogOpen.value = true;
}

function addDriver() {
  getAvailableDrivers();
  driverDialogOpen.value = true;
}

function handleVehicleSelection(selection) {
  selectedVehicles.value = selection;
}

function handleDriverSelection(selection) {
  selectedDrivers.value = selection;
}

function confirmAddVehicle() {
  if (selectedVehicles.value.length === 0) {
    proxy.$modal.msgError("请选择要添加的车辆");
    return;
  }
  
  allocatedVehicles.value.push(...selectedVehicles.value);
  vehicleDialogOpen.value = false;
  selectedVehicles.value = [];
  proxy.$modal.msgSuccess(`成功添加 ${selectedVehicles.value.length} 辆车辆`);
}

function confirmAddDriver() {
  if (selectedDrivers.value.length === 0) {
    proxy.$modal.msgError("请选择要添加的司机");
    return;
  }
  
  allocatedDrivers.value.push(...selectedDrivers.value);
  driverDialogOpen.value = false;
  selectedDrivers.value = [];
  proxy.$modal.msgSuccess(`成功添加 ${selectedDrivers.value.length} 名司机`);
}

function removeVehicle(vehicle) {
  proxy.$modal.confirm(`是否确认移除车辆 ${vehicle.plateNumber}？`).then(() => {
    const index = allocatedVehicles.value.findIndex(v => v.vehicleId === vehicle.vehicleId);
    if (index > -1) {
      allocatedVehicles.value.splice(index, 1);
      proxy.$modal.msgSuccess("车辆移除成功");
    }
  }).catch(() => {});
}

function removeDriver(driver) {
  proxy.$modal.confirm(`是否确认移除司机 ${driver.driverName}？`).then(() => {
    const index = allocatedDrivers.value.findIndex(d => d.driverId === driver.driverId);
    if (index > -1) {
      allocatedDrivers.value.splice(index, 1);
      proxy.$modal.msgSuccess("司机移除成功");
    }
  }).catch(() => {});
}

onMounted(() => {
  getRouteList();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.route-list {
  max-height: 600px;
  overflow-y: auto;
}

.route-item {
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.route-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.route-item.active {
  border-color: #409eff;
  background: #e6f7ff;
}

.route-info {
  margin-bottom: 8px;
}

.route-name {
  font-weight: bold;
  font-size: 16px;
  color: #303133;
}

.route-detail {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.route-stats {
  display: flex;
  gap: 8px;
}

.allocation-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #303133;
}

.statistics-section {
  padding: 20px 0;
}

.stat-card {
  text-align: center;
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>
