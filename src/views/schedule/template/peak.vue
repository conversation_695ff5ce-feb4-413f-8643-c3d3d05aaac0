<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>高峰加密发车设置</span>
          <el-button type="primary" @click="handleSave">保存设置</el-button>
        </div>
      </template>
      
      <el-form ref="peakRef" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option
                  v-for="route in routeOptions"
                  :key="route.routeId"
                  :label="route.routeName"
                  :value="route.routeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-divider content-position="left">基础发车设置</el-divider>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="首班车时间" prop="firstBusTime">
              <el-time-picker
                v-model="form.firstBusTime"
                placeholder="选择首班车时间"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="末班车时间" prop="lastBusTime">
              <el-time-picker
                v-model="form.lastBusTime"
                placeholder="选择末班车时间"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="平峰间隔(分钟)" prop="normalInterval">
              <el-input-number v-model="form.normalInterval" :min="5" :max="30" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-divider content-position="left">高峰时段设置</el-divider>
        
        <div class="peak-periods">
          <div v-for="(period, index) in form.peakPeriods" :key="index" class="peak-period-item">
            <el-card class="period-card">
              <template #header>
                <div class="period-header">
                  <span>{{ period.name }}</span>
                  <el-button 
                    type="danger" 
                    size="small" 
                    icon="Delete" 
                    @click="removePeakPeriod(index)"
                    v-if="form.peakPeriods.length > 1"
                  >删除</el-button>
                </div>
              </template>
              
              <el-row>
                <el-col :span="6">
                  <el-form-item label="时段名称">
                    <el-input v-model="period.name" placeholder="如：早高峰" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="开始时间">
                    <el-time-picker
                      v-model="period.startTime"
                      placeholder="开始时间"
                      value-format="HH:mm"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="结束时间">
                    <el-time-picker
                      v-model="period.endTime"
                      placeholder="结束时间"
                      value-format="HH:mm"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="发车间隔(分钟)">
                    <el-input-number v-model="period.interval" :min="1" :max="15" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-card>
          </div>
        </div>
        
        <el-button type="primary" plain icon="Plus" @click="addPeakPeriod" style="margin-bottom: 20px;">
          添加高峰时段
        </el-button>
        
        <el-divider content-position="left">发车时刻表预览</el-divider>
        
        <div class="schedule-preview">
          <el-button type="info" @click="generateSchedule" style="margin-bottom: 15px;">
            生成时刻表预览
          </el-button>
          
          <el-alert
            :title="`预计发车班次：${scheduleList.length} 班`"
            type="info"
            :closable="false"
            style="margin-bottom: 15px;"
            v-if="scheduleList.length > 0"
          />
          
          <div class="time-slots" v-if="scheduleList.length > 0">
            <el-tag
              v-for="(item, index) in scheduleList"
              :key="index"
              class="time-tag"
              :type="item.isPeak ? 'danger' : 'primary'"
            >
              {{ item.time }}
              <span v-if="item.isPeak" class="peak-label">高峰</span>
            </el-tag>
          </div>
        </div>
        
        <el-divider content-position="left">高级设置</el-divider>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="单程时间(分钟)" prop="singleTripTime">
              <el-input-number v-model="form.singleTripTime" :min="10" :max="300" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关联发车时间(分钟)" prop="linkedDepartureTime">
              <el-input-number v-model="form.linkedDepartureTime" :min="0" :max="30" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否启用" prop="status">
              <el-switch v-model="form.status" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="适用日期类型" prop="dateType">
              <el-select v-model="form.dateType" placeholder="请选择适用日期类型">
                <el-option label="工作日" value="weekday" />
                <el-option label="周末" value="weekend" />
                <el-option label="节假日" value="holiday" />
                <el-option label="全部" value="all" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="模板描述" prop="templateDescription">
          <el-input v-model="form.templateDescription" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup name="PeakTemplate">
const { proxy } = getCurrentInstance();

const routeOptions = ref([]);
const scheduleList = ref([]);

const data = reactive({
  form: {
    templateId: null,
    templateName: null,
    routeId: null,
    routeName: null,
    templateType: "peak",
    firstBusTime: "06:00",
    lastBusTime: "22:00",
    normalInterval: 12,
    singleTripTime: 45,
    linkedDepartureTime: 3,
    status: "1",
    dateType: "weekday",
    templateDescription: null,
    remark: null,
    peakPeriods: [
      {
        name: "早高峰",
        startTime: "07:00",
        endTime: "09:00",
        interval: 5
      },
      {
        name: "晚高峰",
        startTime: "17:00",
        endTime: "19:00",
        interval: 6
      }
    ]
  },
  rules: {
    routeId: [
      { required: true, message: "请选择线路", trigger: "change" }
    ],
    templateName: [
      { required: true, message: "模板名称不能为空", trigger: "blur" }
    ],
    firstBusTime: [
      { required: true, message: "请选择首班车时间", trigger: "change" }
    ],
    lastBusTime: [
      { required: true, message: "请选择末班车时间", trigger: "change" }
    ],
    normalInterval: [
      { required: true, message: "请输入平峰间隔", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

/** 获取线路选项 */
function getRouteOptions() {
  routeOptions.value = [
    { routeId: 1, routeName: "1路" },
    { routeId: 2, routeName: "2路" },
    { routeId: 3, routeName: "3路" }
  ];
}

/** 添加高峰时段 */
function addPeakPeriod() {
  form.value.peakPeriods.push({
    name: `高峰时段${form.value.peakPeriods.length + 1}`,
    startTime: "12:00",
    endTime: "14:00",
    interval: 8
  });
}

/** 删除高峰时段 */
function removePeakPeriod(index) {
  form.value.peakPeriods.splice(index, 1);
}

/** 生成发车时刻表 */
function generateSchedule() {
  if (!form.value.firstBusTime || !form.value.lastBusTime) {
    proxy.$modal.msgError("请先设置首末班车时间");
    return;
  }
  
  const startTime = parseTime(form.value.firstBusTime);
  const endTime = parseTime(form.value.lastBusTime);
  
  if (startTime >= endTime) {
    proxy.$modal.msgError("末班车时间必须晚于首班车时间");
    return;
  }
  
  const schedule = [];
  let currentTime = startTime;
  
  while (currentTime <= endTime) {
    const timeStr = formatTime(currentTime);
    const isPeak = isInPeakPeriod(timeStr);
    const interval = isPeak ? getPeakInterval(timeStr) : form.value.normalInterval;
    
    schedule.push({
      time: timeStr,
      isPeak: isPeak
    });
    
    currentTime += interval;
  }
  
  scheduleList.value = schedule;
}

/** 判断是否在高峰时段 */
function isInPeakPeriod(timeStr) {
  const time = parseTime(timeStr);
  return form.value.peakPeriods.some(period => {
    const start = parseTime(period.startTime);
    const end = parseTime(period.endTime);
    return time >= start && time <= end;
  });
}

/** 获取高峰时段的发车间隔 */
function getPeakInterval(timeStr) {
  const time = parseTime(timeStr);
  const period = form.value.peakPeriods.find(period => {
    const start = parseTime(period.startTime);
    const end = parseTime(period.endTime);
    return time >= start && time <= end;
  });
  return period ? period.interval : form.value.normalInterval;
}

/** 解析时间字符串为分钟数 */
function parseTime(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/** 格式化分钟数为时间字符串 */
function formatTime(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find(r => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
    if (!form.value.templateName) {
      form.value.templateName = `${route.routeName}高峰加密模板`;
    }
  }
}

function handleSave() {
  proxy.$refs["peakRef"].validate(valid => {
    if (valid) {
      // 验证高峰时段设置
      for (let i = 0; i < form.value.peakPeriods.length; i++) {
        const period = form.value.peakPeriods[i];
        if (!period.startTime || !period.endTime || !period.interval) {
          proxy.$modal.msgError(`请完善第${i + 1}个高峰时段的设置`);
          return;
        }
        if (parseTime(period.startTime) >= parseTime(period.endTime)) {
          proxy.$modal.msgError(`第${i + 1}个高峰时段的结束时间必须晚于开始时间`);
          return;
        }
      }
      
      proxy.$modal.msgSuccess("高峰加密发车模板保存成功");
      // 这里可以调用API保存数据
    }
  });
}

onMounted(() => {
  getRouteOptions();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.peak-periods {
  margin-bottom: 20px;
}

.peak-period-item {
  margin-bottom: 15px;
}

.period-card {
  border: 1px solid #e4e7ed;
}

.period-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-preview {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.time-tag {
  margin: 2px;
  position: relative;
}

.peak-label {
  font-size: 10px;
  margin-left: 4px;
}
</style>
