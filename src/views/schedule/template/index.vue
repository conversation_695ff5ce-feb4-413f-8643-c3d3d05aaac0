<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="模板名称" prop="templateName">
        <el-input
          v-model="queryParams.templateName"
          placeholder="请输入模板名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="线路" prop="routeId">
        <el-select v-model="queryParams.routeId" placeholder="请选择线路" clearable>
          <el-option
            v-for="route in routeOptions"
            :key="route.routeId"
            :label="route.routeName"
            :value="route.routeId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Setting" @click="handleFixedTemplate">固定间隔</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="TrendCharts" @click="handlePeakTemplate">高峰加密</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" @click="handleCustomTemplate">自定义排班</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="模板ID" align="center" prop="templateId" />
      <el-table-column label="模板名称" align="center" prop="templateName" />
      <el-table-column label="线路" align="center" prop="routeName" />
      <el-table-column label="模板类型" align="center" prop="templateType">
        <template #default="scope">
          <el-tag v-if="scope.row.templateType === 'fixed'" type="info">固定间隔</el-tag>
          <el-tag v-else-if="scope.row.templateType === 'peak'" type="warning">高峰加密</el-tag>
          <el-tag v-else type="success">自定义</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="发车间隔(分钟)" align="center" prop="departureInterval" />
      <el-table-column label="首班车时间" align="center" prop="firstBusTime" />
      <el-table-column label="末班车时间" align="center" prop="lastBusTime" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag v-if="scope.row.status === '1'" type="success">启用</el-tag>
          <el-tag v-else type="danger">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改模板对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="templateRef" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option
                  v-for="route in routeOptions"
                  :key="route.routeId"
                  :label="route.routeName"
                  :value="route.routeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="模板类型" prop="templateType">
              <el-select v-model="form.templateType" placeholder="请选择模板类型">
                <el-option label="固定间隔" value="fixed" />
                <el-option label="高峰加密" value="peak" />
                <el-option label="自定义" value="custom" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态">
                <el-option label="启用" value="1" />
                <el-option label="停用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="首班车时间" prop="firstBusTime">
              <el-time-picker
                v-model="form.firstBusTime"
                placeholder="选择首班车时间"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="末班车时间" prop="lastBusTime">
              <el-time-picker
                v-model="form.lastBusTime"
                placeholder="选择末班车时间"
                value-format="HH:mm"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="发车间隔(分钟)" prop="departureInterval">
              <el-input-number v-model="form.departureInterval" :min="1" :max="60" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单程时间(分钟)" prop="singleTripTime">
              <el-input-number v-model="form.singleTripTime" :min="10" :max="300" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模板描述" prop="templateDescription">
          <el-input v-model="form.templateDescription" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ScheduleTemplate">
const { proxy } = getCurrentInstance();

const templateList = ref([]);
const routeOptions = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateName: null,
    routeId: null,
    status: null
  },
  rules: {
    templateName: [
      { required: true, message: "模板名称不能为空", trigger: "blur" }
    ],
    routeId: [
      { required: true, message: "请选择线路", trigger: "change" }
    ],
    templateType: [
      { required: true, message: "请选择模板类型", trigger: "change" }
    ],
    firstBusTime: [
      { required: true, message: "请选择首班车时间", trigger: "change" }
    ],
    lastBusTime: [
      { required: true, message: "请选择末班车时间", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询模板列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      templateId: 1,
      templateName: "1路固定间隔模板",
      routeId: 1,
      routeName: "1路",
      templateType: "fixed",
      departureInterval: 8,
      firstBusTime: "05:30",
      lastBusTime: "22:30",
      singleTripTime: 45,
      templateDescription: "1路线固定间隔发车模板，适用于平峰时段",
      status: "1",
      remark: "主要线路模板"
    },
    {
      templateId: 2,
      templateName: "2路高峰加密模板",
      routeId: 2,
      routeName: "2路",
      templateType: "peak",
      departureInterval: 5,
      firstBusTime: "06:00",
      lastBusTime: "21:30",
      singleTripTime: 55,
      templateDescription: "2路线高峰时段加密发车模板",
      status: "1",
      remark: "机场专线高峰模板"
    },
    {
      templateId: 3,
      templateName: "3路自定义模板",
      routeId: 3,
      routeName: "3路",
      templateType: "custom",
      departureInterval: 12,
      firstBusTime: "06:30",
      lastBusTime: "20:00",
      singleTripTime: 40,
      templateDescription: "3路线自定义排班模板",
      status: "0",
      remark: "临时停用"
    }
  ];
  
  setTimeout(() => {
    templateList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 获取线路选项 */
function getRouteOptions() {
  routeOptions.value = [
    { routeId: 1, routeName: "1路" },
    { routeId: 2, routeName: "2路" },
    { routeId: 3, routeName: "3路" }
  ];
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.templateId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getRouteOptions();
  open.value = true;
  title.value = "添加排班模板";
}

function handleUpdate(row) {
  reset();
  getRouteOptions();
  form.value = { ...row };
  open.value = true;
  title.value = "修改排班模板";
}

function handleDetail(row) {
  proxy.$modal.msgInfo(`查看模板详情：${row.templateName}`);
}

function handleDelete(row) {
  const templateIds = row.templateId || ids.value;
  proxy.$modal.confirm('是否确认删除模板编号为"' + templateIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleFixedTemplate() {
  proxy.$router.push('/schedule/template/fixed');
}

function handlePeakTemplate() {
  proxy.$router.push('/schedule/template/peak');
}

function handleCustomTemplate() {
  proxy.$router.push('/schedule/template/custom');
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find(r => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
  }
}

function submitForm() {
  proxy.$refs["templateRef"].validate(valid => {
    if (valid) {
      if (form.value.templateId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    templateId: null,
    templateName: null,
    routeId: null,
    routeName: null,
    templateType: null,
    departureInterval: 10,
    firstBusTime: null,
    lastBusTime: null,
    singleTripTime: null,
    templateDescription: null,
    status: "1",
    remark: null
  };
  proxy.resetForm("templateRef");
}

onMounted(() => {
  getRouteOptions();
  getList();
});
</script>
