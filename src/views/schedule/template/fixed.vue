<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>固定间隔发车设置</span>
          <el-button type="primary" @click="handleSave">保存设置</el-button>
        </div>
      </template>
      
      <el-form ref="fixedRef" :model="form" :rules="rules" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option
                  v-for="route in routeOptions"
                  :key="route.routeId"
                  :label="route.routeName"
                  :value="route.routeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板名称" prop="templateName">
              <el-input v-model="form.templateName" placeholder="请输入模板名称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-divider content-position="left">基础时间设置</el-divider>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="首班车时间" prop="firstBusTime">
              <el-time-picker
                v-model="form.firstBusTime"
                placeholder="选择首班车时间"
                value-format="HH:mm"
                @change="calculateSchedule"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="末班车时间" prop="lastBusTime">
              <el-time-picker
                v-model="form.lastBusTime"
                placeholder="选择末班车时间"
                value-format="HH:mm"
                @change="calculateSchedule"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发车间隔(分钟)" prop="departureInterval">
              <el-input-number 
                v-model="form.departureInterval" 
                :min="1" 
                :max="60" 
                @change="calculateSchedule"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row>
          <el-col :span="8">
            <el-form-item label="单程时间(分钟)" prop="singleTripTime">
              <el-input-number v-model="form.singleTripTime" :min="10" :max="300" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="关联发车时间(分钟)" prop="linkedDepartureTime">
              <el-input-number v-model="form.linkedDepartureTime" :min="0" :max="30" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="需要车辆数" prop="requiredVehicles">
              <el-input-number v-model="form.requiredVehicles" :min="1" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-divider content-position="left">发车时刻表预览</el-divider>
        
        <div class="schedule-preview">
          <el-alert
            :title="`预计发车班次：${scheduleList.length} 班，需要车辆：${form.requiredVehicles} 辆`"
            type="info"
            :closable="false"
            style="margin-bottom: 15px;"
          />
          
          <div class="time-slots">
            <el-tag
              v-for="(time, index) in scheduleList"
              :key="index"
              class="time-tag"
              :type="index % 2 === 0 ? 'primary' : 'success'"
            >
              {{ time }}
            </el-tag>
          </div>
        </div>
        
        <el-divider content-position="left">高级设置</el-divider>
        
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否启用" prop="status">
              <el-switch v-model="form.status" active-value="1" inactive-value="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="适用日期类型" prop="dateType">
              <el-select v-model="form.dateType" placeholder="请选择适用日期类型">
                <el-option label="工作日" value="weekday" />
                <el-option label="周末" value="weekend" />
                <el-option label="节假日" value="holiday" />
                <el-option label="全部" value="all" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="模板描述" prop="templateDescription">
          <el-input v-model="form.templateDescription" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </el-form-item>
        
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup name="FixedTemplate">
const { proxy } = getCurrentInstance();

const routeOptions = ref([]);
const scheduleList = ref([]);

const data = reactive({
  form: {
    templateId: null,
    templateName: null,
    routeId: null,
    routeName: null,
    templateType: "fixed",
    firstBusTime: "06:00",
    lastBusTime: "22:00",
    departureInterval: 10,
    singleTripTime: 45,
    linkedDepartureTime: 3,
    requiredVehicles: 0,
    status: "1",
    dateType: "weekday",
    templateDescription: null,
    remark: null
  },
  rules: {
    routeId: [
      { required: true, message: "请选择线路", trigger: "change" }
    ],
    templateName: [
      { required: true, message: "模板名称不能为空", trigger: "blur" }
    ],
    firstBusTime: [
      { required: true, message: "请选择首班车时间", trigger: "change" }
    ],
    lastBusTime: [
      { required: true, message: "请选择末班车时间", trigger: "change" }
    ],
    departureInterval: [
      { required: true, message: "请输入发车间隔", trigger: "blur" }
    ]
  }
});

const { form, rules } = toRefs(data);

/** 获取线路选项 */
function getRouteOptions() {
  routeOptions.value = [
    { routeId: 1, routeName: "1路" },
    { routeId: 2, routeName: "2路" },
    { routeId: 3, routeName: "3路" }
  ];
}

/** 计算发车时刻表 */
function calculateSchedule() {
  if (!form.value.firstBusTime || !form.value.lastBusTime || !form.value.departureInterval) {
    scheduleList.value = [];
    form.value.requiredVehicles = 0;
    return;
  }
  
  const startTime = parseTime(form.value.firstBusTime);
  const endTime = parseTime(form.value.lastBusTime);
  const interval = form.value.departureInterval;
  
  if (startTime >= endTime) {
    scheduleList.value = [];
    form.value.requiredVehicles = 0;
    return;
  }
  
  const schedule = [];
  let currentTime = startTime;
  
  while (currentTime <= endTime) {
    schedule.push(formatTime(currentTime));
    currentTime += interval;
  }
  
  scheduleList.value = schedule;
  
  // 计算需要的车辆数量
  const singleTripTime = form.value.singleTripTime || 45;
  const linkedTime = form.value.linkedDepartureTime || 3;
  const totalTripTime = singleTripTime * 2 + linkedTime; // 往返时间 + 关联时间
  form.value.requiredVehicles = Math.ceil(totalTripTime / interval);
}

/** 解析时间字符串为分钟数 */
function parseTime(timeStr) {
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

/** 格式化分钟数为时间字符串 */
function formatTime(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find(r => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
    if (!form.value.templateName) {
      form.value.templateName = `${route.routeName}固定间隔模板`;
    }
  }
}

function handleSave() {
  proxy.$refs["fixedRef"].validate(valid => {
    if (valid) {
      if (scheduleList.value.length === 0) {
        proxy.$modal.msgError("请先设置正确的发车时间和间隔");
        return;
      }
      proxy.$modal.msgSuccess("固定间隔发车模板保存成功");
      // 这里可以调用API保存数据
    }
  });
}

onMounted(() => {
  getRouteOptions();
  calculateSchedule();
});

// 监听表单变化，自动计算时刻表
watch([
  () => form.value.firstBusTime,
  () => form.value.lastBusTime,
  () => form.value.departureInterval,
  () => form.value.singleTripTime,
  () => form.value.linkedDepartureTime
], () => {
  calculateSchedule();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-preview {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.time-slots {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.time-tag {
  margin: 2px;
}
</style>
