<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="车牌号" prop="plateNumber">
        <el-input
          v-model="queryParams.plateNumber"
          placeholder="请输入车牌号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行驶日期" prop="drivingDate">
        <el-date-picker
          v-model="queryParams.drivingDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item label="司机" prop="driverName">
        <el-input
          v-model="queryParams.driverName"
          placeholder="请输入司机姓名"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="info" plain icon="Back" @click="handleBack">返回</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="Position" @click="handleTrackReplay">轨迹回放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="drivingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="记录ID" align="center" prop="drivingId" />
      <el-table-column label="车牌号" align="center" prop="plateNumber" />
      <el-table-column label="司机" align="center" prop="driverName" />
      <el-table-column label="线路" align="center" prop="routeName" />
      <el-table-column label="出车时间" align="center" prop="startTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="收车时间" align="center" prop="endTime" width="150">
        <template #default="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d} {h}:{i}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="行驶里程(km)" align="center" prop="mileage" />
      <el-table-column label="运行时长" align="center" prop="duration" />
      <el-table-column label="平均速度(km/h)" align="center" prop="avgSpeed" />
      <el-table-column label="最高速度(km/h)" align="center" prop="maxSpeed" />
      <el-table-column label="载客人次" align="center" prop="passengerCount" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">详情</el-button>
          <el-button link type="primary" icon="Position" @click="handleTrackReplay(scope.row)">轨迹</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改行驶记录对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="drivingRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="车辆" prop="vehicleId">
              <el-select v-model="form.vehicleId" placeholder="请选择车辆" @change="handleVehicleChange">
                <el-option
                  v-for="vehicle in vehicleOptions"
                  :key="vehicle.vehicleId"
                  :label="`${vehicle.plateNumber}(${vehicle.vehicleNumber})`"
                  :value="vehicle.vehicleId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="司机" prop="driverId">
              <el-select v-model="form.driverId" placeholder="请选择司机" @change="handleDriverChange">
                <el-option
                  v-for="driver in driverOptions"
                  :key="driver.driverId"
                  :label="`${driver.driverName}(${driver.employeeId})`"
                  :value="driver.driverId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="线路" prop="routeId">
              <el-select v-model="form.routeId" placeholder="请选择线路" @change="handleRouteChange">
                <el-option
                  v-for="route in routeOptions"
                  :key="route.routeId"
                  :label="route.routeName"
                  :value="route.routeId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行驶日期" prop="drivingDate">
              <el-date-picker
                v-model="form.drivingDate"
                type="date"
                placeholder="选择行驶日期"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="出车时间" prop="startTime">
              <el-time-picker
                v-model="form.startTime"
                placeholder="选择出车时间"
                value-format="HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收车时间" prop="endTime">
              <el-time-picker
                v-model="form.endTime"
                placeholder="选择收车时间"
                value-format="HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="行驶里程(km)" prop="mileage">
              <el-input-number v-model="form.mileage" :precision="1" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="载客人次" prop="passengerCount">
              <el-input-number v-model="form.passengerCount" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="平均速度(km/h)" prop="avgSpeed">
              <el-input-number v-model="form.avgSpeed" :precision="1" :min="0" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最高速度(km/h)" prop="maxSpeed">
              <el-input-number v-model="form.maxSpeed" :precision="1" :min="0" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 轨迹回放对话框 -->
    <el-dialog title="轨迹回放" v-model="trackOpen" width="80%" append-to-body>
      <div id="trackMap" style="height: 500px; width: 100%;"></div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="trackOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="VehicleDriving">
const { proxy } = getCurrentInstance();

const drivingList = ref([]);
const vehicleOptions = ref([]);
const driverOptions = ref([]);
const routeOptions = ref([]);
const open = ref(false);
const trackOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    plateNumber: null,
    drivingDate: null,
    driverName: null
  },
  rules: {
    vehicleId: [
      { required: true, message: "请选择车辆", trigger: "change" }
    ],
    driverId: [
      { required: true, message: "请选择司机", trigger: "change" }
    ],
    routeId: [
      { required: true, message: "请选择线路", trigger: "change" }
    ],
    drivingDate: [
      { required: true, message: "请选择行驶日期", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询行驶记录列表 */
function getList() {
  loading.value = true;
  // 模拟数据
  const mockData = [
    {
      drivingId: 1,
      vehicleId: 1,
      plateNumber: "京A12345",
      driverId: 1,
      driverName: "张三",
      routeId: 1,
      routeName: "1路",
      drivingDate: "2024-01-15",
      startTime: "2024-01-15 06:00:00",
      endTime: "2024-01-15 18:30:00",
      mileage: 285.6,
      duration: "12小时30分",
      avgSpeed: 22.8,
      maxSpeed: 45.2,
      passengerCount: 1250,
      remark: "正常运营"
    },
    {
      drivingId: 2,
      vehicleId: 2,
      plateNumber: "京A12346",
      driverId: 2,
      driverName: "李四",
      routeId: 2,
      routeName: "2路",
      drivingDate: "2024-01-15",
      startTime: "2024-01-15 06:30:00",
      endTime: "2024-01-15 19:00:00",
      mileage: 320.4,
      duration: "12小时30分",
      avgSpeed: 25.6,
      maxSpeed: 48.5,
      passengerCount: 1380,
      remark: "正常运营"
    }
  ];

  setTimeout(() => {
    drivingList.value = mockData;
    total.value = mockData.length;
    loading.value = false;
  }, 500);
}

/** 获取选项数据 */
function getOptions() {
  vehicleOptions.value = [
    { vehicleId: 1, plateNumber: "京A12345", vehicleNumber: "V001" },
    { vehicleId: 2, plateNumber: "京A12346", vehicleNumber: "V002" }
  ];

  driverOptions.value = [
    { driverId: 1, driverName: "张三", employeeId: "D001" },
    { driverId: 2, driverName: "李四", employeeId: "D002" }
  ];

  routeOptions.value = [
    { routeId: 1, routeName: "1路" },
    { routeId: 2, routeName: "2路" },
    { routeId: 3, routeName: "3路" }
  ];
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.drivingId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function handleAdd() {
  reset();
  getOptions();
  open.value = true;
  title.value = "添加行驶记录";
}

function handleUpdate(row) {
  reset();
  getOptions();
  form.value = { ...row };
  open.value = true;
  title.value = "修改行驶记录";
}

function handleDetail(row) {
  proxy.$modal.msgInfo(`查看行驶记录详情：${row.plateNumber} - ${row.driverName}`);
}

function handleDelete(row) {
  const drivingIds = row.drivingId || ids.value;
  proxy.$modal.confirm('是否确认删除行驶记录编号为"' + drivingIds + '"的数据项？').then(function() {
    proxy.$modal.msgSuccess("删除成功");
    getList();
  }).catch(() => {});
}

function handleTrackReplay(row) {
  trackOpen.value = true;
  nextTick(() => {
    // 这里可以集成地图组件显示轨迹回放
    proxy.$modal.msgInfo("轨迹回放功能需要集成地图组件");
  });
}

function handleBack() {
  proxy.$router.push('/basic/vehicle');
}

function handleExport() {
  proxy.$modal.confirm('是否确认导出所有行驶记录数据项？').then(() => {
    proxy.$modal.msgSuccess("导出成功");
  });
}

function handleVehicleChange(vehicleId) {
  const vehicle = vehicleOptions.value.find(v => v.vehicleId === vehicleId);
  if (vehicle) {
    form.value.plateNumber = vehicle.plateNumber;
  }
}

function handleDriverChange(driverId) {
  const driver = driverOptions.value.find(d => d.driverId === driverId);
  if (driver) {
    form.value.driverName = driver.driverName;
  }
}

function handleRouteChange(routeId) {
  const route = routeOptions.value.find(r => r.routeId === routeId);
  if (route) {
    form.value.routeName = route.routeName;
  }
}

function submitForm() {
  proxy.$refs["drivingRef"].validate(valid => {
    if (valid) {
      if (form.value.drivingId != null) {
        proxy.$modal.msgSuccess("修改成功");
      } else {
        proxy.$modal.msgSuccess("新增成功");
      }
      open.value = false;
      getList();
    }
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    drivingId: null,
    vehicleId: null,
    plateNumber: null,
    driverId: null,
    driverName: null,
    routeId: null,
    routeName: null,
    drivingDate: null,
    startTime: null,
    endTime: null,
    mileage: null,
    duration: null,
    avgSpeed: null,
    maxSpeed: null,
    passengerCount: null,
    remark: null
  };
  proxy.resetForm("drivingRef");
}

getList();
</script>
