<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>统计报表</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>
      
      <div class="statistics-content">
        <el-alert
          title="统计报表"
          description="显示车辆总数、司机总数、线路总数等统计信息。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="统计报表功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="DisplayStatistics">
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-content {
  padding: 20px;
}
</style>
