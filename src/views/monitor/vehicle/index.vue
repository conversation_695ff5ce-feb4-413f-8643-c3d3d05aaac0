<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>车辆监控</span>
          <div>
            <el-button type="primary" @click="handleLocation">位置跟踪</el-button>
            <el-button type="info" @click="handleStatus">状态监控</el-button>
            <el-button type="warning" @click="handleAlert">异常告警</el-button>
          </div>
        </div>
      </template>
      
      <div class="monitor-content">
        <el-alert
          title="车辆监控中心"
          description="实时监控所有运营车辆的位置、状态和异常情况。"
          type="info"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="车辆监控功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="VehicleMonitor">
const { proxy } = getCurrentInstance();

function handleLocation() {
  proxy.$router.push('/monitor/vehicle/location');
}

function handleStatus() {
  proxy.$router.push('/monitor/vehicle/status');
}

function handleAlert() {
  proxy.$router.push('/monitor/vehicle/alert');
}
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-content {
  padding: 20px;
}
</style>
