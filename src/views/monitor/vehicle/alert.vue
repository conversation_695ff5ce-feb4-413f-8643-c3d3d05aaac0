<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>异常情况告警</span>
          <el-button @click="$router.go(-1)">返回</el-button>
        </div>
      </template>
      
      <div class="alert-content">
        <el-alert
          title="异常情况告警"
          description="监控和处理车辆超速、故障等异常情况。"
          type="warning"
          :closable="false"
          style="margin-bottom: 20px;"
        />
        
        <el-empty description="异常情况告警功能开发中" />
      </div>
    </el-card>
  </div>
</template>

<script setup name="VehicleAlert">
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-content {
  padding: 20px;
}
</style>
